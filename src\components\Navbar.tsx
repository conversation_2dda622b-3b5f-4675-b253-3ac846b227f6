import React, { useEffect, useState } from 'react';
import Button from './Button';
import './Navbar.css';
import { gsap } from 'gsap';
import { ScrollToPlugin } from 'gsap/ScrollToPlugin';

// Register ScrollToPlugin
gsap.registerPlugin(ScrollToPlugin);

interface NavbarProps {
  onJoinWaitlist?: () => void;
}

const Navigation = () => {
  // Handle URL hash navigation on page load
  useEffect(() => {
    const handleHashNavigation = () => {
      const hash = window.location.hash.substring(1); // Remove the # symbol
      if (hash) {
        // Small delay to ensure page is fully loaded
        setTimeout(() => {
          const targetElement = document.getElementById(hash);
          if (targetElement) {
            const scrollOffset = 100;
            gsap.to(window, {
              duration: 1.5,
              scrollTo: {
                y: targetElement,
                offsetY: scrollOffset
              },
              ease: "power2.inOut"
            });
          } else if (hash === 'home') {
            // Scroll to top for home
            gsap.to(window, {
              duration: 1.5,
              scrollTo: { y: 0 },
              ease: "power2.inOut"
            });
          }
        }, 100);
      }
    };

    // Handle initial page load with hash
    handleHashNavigation();

    // Handle hash changes (browser back/forward)
    window.addEventListener('hashchange', handleHashNavigation);

    return () => {
      window.removeEventListener('hashchange', handleHashNavigation);
    };
  }, []);

  const handleSmoothScroll = (e: React.MouseEvent<HTMLAnchorElement>, targetId: string) => {
    e.preventDefault();

    // Check if we're on the homepage (has .homepage element)
    const isOnHomepage = document.querySelector('.homepage') !== null;

    if (!isOnHomepage) {
      // If not on homepage, navigate to homepage first, then scroll
      window.location.href = `/#${targetId}`;
      return;
    }

    let targetElement: HTMLElement | null = null;
    const scrollOffset = 100; // Account for fixed navbar height and spacing

    // Handle different target sections
    switch (targetId) {
      case 'home':
        // Scroll to the very top of the page (hero section)
        gsap.to(window, {
          duration: 1.5,
          scrollTo: { y: 0 },
          ease: "power2.inOut"
        });
        return;

      case 'features':
        targetElement = document.getElementById('features');
        break;

      case 'why-us':
        targetElement = document.getElementById('why-us');
        break;

      default:
        targetElement = document.getElementById(targetId);
    }

    if (targetElement) {
      // Use GSAP for smooth scrolling with proper offset
      gsap.to(window, {
        duration: 1.5,
        scrollTo: {
          y: targetElement,
          offsetY: scrollOffset
        },
        ease: "power2.inOut"
      });
    }
  };

  return (
    <div className="navbar__nav">
      <a href="#home" className="navbar__nav-link" onClick={(e) => handleSmoothScroll(e, 'home')}>Home</a>
      <span className="navbar__nav-separator"> • </span>
      <a href="#features" className="navbar__nav-link" onClick={(e) => handleSmoothScroll(e, 'features')}>Features</a>
      <span className="navbar__nav-separator"> • </span>
      <a href="#why-us" className="navbar__nav-link" onClick={(e) => handleSmoothScroll(e, 'why-us')}>Why Us</a>
    </div>
  );
};

export default function Navbar({ onJoinWaitlist }: NavbarProps) {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      const threshold = 80; // Scroll threshold in pixels

      if (scrollPosition > threshold) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll);

    // Cleanup function to remove event listener
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <nav className={`navbar ${isScrolled ? 'navbar--scrolled' : ''}`}>
      <div className="navbar__brand">
        LawVriksh
      </div>

      <div className="navbar__content">
        <Navigation />

        <Button onClick={onJoinWaitlist}>
          Join Waitlist
        </Button>
      </div>
    </nav>
  );
}
